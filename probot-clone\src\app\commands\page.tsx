import Link from 'next/link';
import { FaHome, FaSearch } from 'react-icons/fa';

const commandCategories = [
  {
    name: 'General',
    commands: [
      { name: '/help', description: 'Shows all available commands', usage: '/help [command]' },
      { name: '/ping', description: 'Check bot latency', usage: '/ping' },
      { name: '/info', description: 'Get information about the bot', usage: '/info' },
      { name: '/invite', description: 'Get bot invite link', usage: '/invite' },
    ]
  },
  {
    name: 'Moderation',
    commands: [
      { name: '/ban', description: 'Ban a user from the server', usage: '/ban @user [reason]' },
      { name: '/kick', description: 'Kick a user from the server', usage: '/kick @user [reason]' },
      { name: '/mute', description: 'Mute a user', usage: '/mute @user [duration] [reason]' },
      { name: '/unmute', description: 'Unmute a user', usage: '/unmute @user' },
      { name: '/warn', description: 'Warn a user', usage: '/warn @user [reason]' },
      { name: '/clear', description: 'Delete messages', usage: '/clear [amount]' },
    ]
  },
  {
    name: 'Welcome & Goodbye',
    commands: [
      { name: '/welcome', description: 'Configure welcome messages', usage: '/welcome setup' },
      { name: '/goodbye', description: 'Configure goodbye messages', usage: '/goodbye setup' },
      { name: '/welcome-test', description: 'Test welcome message', usage: '/welcome-test' },
    ]
  },
  {
    name: 'Levels',
    commands: [
      { name: '/rank', description: 'Check your or someone\'s rank', usage: '/rank [@user]' },
      { name: '/leaderboard', description: 'Show server leaderboard', usage: '/leaderboard' },
      { name: '/level-roles', description: 'Configure level roles', usage: '/level-roles setup' },
    ]
  },
  {
    name: 'Reaction Roles',
    commands: [
      { name: '/reaction-role', description: 'Create reaction roles', usage: '/reaction-role create' },
      { name: '/button-role', description: 'Create button roles', usage: '/button-role create' },
      { name: '/dropdown-role', description: 'Create dropdown roles', usage: '/dropdown-role create' },
    ]
  },
  {
    name: 'Embed',
    commands: [
      { name: '/embed', description: 'Create custom embed messages', usage: '/embed create' },
      { name: '/embed-edit', description: 'Edit existing embed', usage: '/embed-edit [message-id]' },
    ]
  },
  {
    name: 'Auto Moderation',
    commands: [
      { name: '/automod', description: 'Configure auto moderation', usage: '/automod setup' },
      { name: '/automod-whitelist', description: 'Whitelist channels/roles', usage: '/automod-whitelist add' },
    ]
  },
  {
    name: 'Logs',
    commands: [
      { name: '/logs', description: 'Configure server logs', usage: '/logs setup' },
      { name: '/logs-channel', description: 'Set logs channel', usage: '/logs-channel #channel' },
    ]
  },
  {
    name: 'Premium',
    commands: [
      { name: '/premium', description: 'Check premium status', usage: '/premium status' },
      { name: '/anti-raid', description: 'Configure anti-raid protection', usage: '/anti-raid setup', premium: true },
      { name: '/vip-protection', description: 'Configure VIP protection', usage: '/vip-protection setup', premium: true },
    ]
  }
];

export default function Commands() {
  return (
    <div className="min-h-screen bg-[#36393f]">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 bg-[#2f3136] border-b border-[#40444b]">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-[#5865f2] rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          <span className="text-white font-bold text-xl">ProBot</span>
        </div>

        <div className="hidden md:flex items-center space-x-8">
          <Link href="/" className="text-[#b9bbbe] hover:text-white transition-colors flex items-center">
            <FaHome className="mr-2" />
            Home
          </Link>
          <Link href="/dashboard" className="text-[#b9bbbe] hover:text-white transition-colors">
            Dashboard
          </Link>
          <Link href="/docs" className="text-[#b9bbbe] hover:text-white transition-colors">
            Docs
          </Link>
        </div>

        <Link
          href="/dashboard"
          className="bg-[#5865f2] hover:bg-[#4752c4] text-white px-6 py-2 rounded transition-colors font-medium"
        >
          Dashboard
        </Link>
      </nav>

      {/* Header */}
      <div className="container mx-auto px-6 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
            ProBot Commands
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Complete list of all ProBot commands with descriptions and usage examples
          </p>

          {/* Search Bar */}
          <div className="max-w-md mx-auto relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search commands..."
              className="w-full pl-10 pr-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            />
          </div>
        </div>

        {/* Commands Grid */}
        <div className="grid gap-8">
          {commandCategories.map((category, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
                {category.name}
                <span className="ml-3 text-sm bg-blue-600 text-white px-2 py-1 rounded-full">
                  {category.commands.length} commands
                </span>
              </h2>

              <div className="grid md:grid-cols-2 gap-4">
                {category.commands.map((command, cmdIndex) => (
                  <div key={cmdIndex} className="bg-black/20 rounded-lg p-4 border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <code className="text-blue-300 font-mono font-semibold">
                        {command.name}
                      </code>
                      {command.premium && (
                        <span className="text-xs bg-yellow-500 text-black px-2 py-1 rounded-full font-semibold">
                          PREMIUM
                        </span>
                      )}
                    </div>
                    <p className="text-gray-300 text-sm mb-3">
                      {command.description}
                    </p>
                    <div className="bg-gray-800/50 rounded p-2">
                      <span className="text-xs text-gray-400">Usage:</span>
                      <code className="text-green-400 font-mono text-sm ml-2">
                        {command.usage}
                      </code>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Footer Note */}
        <div className="text-center mt-12 p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10">
          <h3 className="text-lg font-semibold text-white mb-2">Need Help?</h3>
          <p className="text-gray-300 mb-4">
            Join our support server for assistance with commands and configuration
          </p>
          <div className="flex justify-center space-x-4">
            <a
              href="https://discord.gg/ProBot"
              className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Join Support Server
            </a>
            <Link
              href="/docs"
              className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Read Documentation
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
