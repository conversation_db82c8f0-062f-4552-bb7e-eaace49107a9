import Link from 'next/link';
import { FaHome, FaBook, FaCog, FaUsers, FaShieldAlt, FaRobot, FaChartLine, FaPalette, FaVolumeUp, FaStar, FaEye } from 'react-icons/fa';

const docSections = [
  {
    title: 'Getting Started',
    icon: FaBook,
    color: 'bg-blue-500',
    items: [
      { name: 'Setup ProBot', description: 'Step by step guide to invite <PERSON>Bot to your server' },
      { name: 'Dashboard Setup', description: 'Learn how to use the web dashboard' },
      { name: 'Frequently Asked Questions', description: 'Common questions and answers' },
      { name: 'Premium Features', description: 'Overview of premium subscription benefits' },
      { name: 'Commands Overview', description: 'Complete list of all available commands' },
      { name: 'Variables', description: 'Dynamic variables you can use in messages' },
    ]
  },
  {
    title: 'Modules',
    icon: FaCog,
    color: 'bg-purple-500',
    items: [
      { name: 'Welcome & Goodbye', description: 'Configure welcome and goodbye messages with custom images' },
      { name: 'Auto Responder', description: 'Set up automatic responses to specific triggers' },
      { name: 'Embed Messages', description: 'Create beautiful rich embed messages' },
      { name: 'Level System', description: 'Reward active members with levels and roles' },
      { name: 'Auto-Roles', description: 'Automatically assign roles to new members' },
      { name: 'Logs', description: 'Monitor all server activities with detailed logs' },
      { name: 'Colors', description: 'Let members choose their own color roles' },
      { name: 'Self-Assignable Roles', description: 'Reaction roles, button roles, and dropdown menus' },
      { name: 'Temporary Channels', description: 'Create temporary voice channels for users' },
      { name: 'Temp Links', description: 'Generate temporary invite links' },
      { name: 'Voice Online', description: 'Display voice channel activity' },
      { name: 'Anti-Raid', description: 'Protect your server from raids and spam' },
      { name: 'VIP Protection', description: 'Advanced protection for VIP members' },
      { name: 'Starboard', description: 'Highlight popular messages with stars' },
      { name: 'Automod', description: 'Automatic moderation system' },
    ]
  }
];

const quickLinks = [
  { name: 'ProBot Website', url: '/', icon: FaHome },
  { name: 'Support Server', url: 'https://discord.gg/probot', icon: FaUsers },
  { name: 'Commands', url: '/commands', icon: FaRobot },
  { name: 'Dashboard', url: '/dashboard', icon: FaCog },
];

export default function Docs() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 bg-black/20 backdrop-blur-sm">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          <span className="text-white font-bold text-xl">ProBot Docs</span>
        </div>
        
        <div className="hidden md:flex items-center space-x-8">
          <Link href="/" className="text-white hover:text-blue-300 transition-colors flex items-center">
            <FaHome className="mr-2" />
            Home
          </Link>
          <Link href="/dashboard" className="text-white hover:text-blue-300 transition-colors">
            Dashboard
          </Link>
          <Link href="/commands" className="text-white hover:text-blue-300 transition-colors">
            Commands
          </Link>
        </div>

        <Link 
          href="/dashboard"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
        >
          Dashboard
        </Link>
      </nav>

      {/* Header */}
      <div className="container mx-auto px-6 py-12">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
            ProBot Documentation
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            ProBot is a highly configurable, multipurpose bot that offers features like image greeting, detailed logs, social commands, moderation, self-assignable roles, social media notifications, and protection for your server.
          </p>
        </div>

        {/* Quick Links */}
        <div className="grid md:grid-cols-4 gap-6 mb-16">
          {quickLinks.map((link, index) => (
            <Link 
              key={index}
              href={link.url}
              className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-colors group"
            >
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center group-hover:bg-blue-700 transition-colors">
                  <link.icon className="text-white" />
                </div>
                <span className="text-white font-semibold">{link.name}</span>
              </div>
            </Link>
          ))}
        </div>

        {/* Documentation Sections */}
        <div className="grid gap-12">
          {docSections.map((section, sectionIndex) => (
            <div key={sectionIndex} className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <div className="flex items-center mb-8">
                <div className={`w-12 h-12 ${section.color} rounded-lg flex items-center justify-center mr-4`}>
                  <section.icon className="text-white text-xl" />
                </div>
                <h2 className="text-3xl font-bold text-white">{section.title}</h2>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {section.items.map((item, itemIndex) => (
                  <div key={itemIndex} className="bg-black/20 rounded-lg p-6 border border-white/10 hover:bg-black/30 transition-colors cursor-pointer group">
                    <h3 className="text-lg font-semibold text-white mb-3 group-hover:text-blue-300 transition-colors">
                      {item.name}
                    </h3>
                    <p className="text-gray-300 text-sm">
                      {item.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* About Our Docs */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mt-16">
          <h2 className="text-3xl font-bold text-white mb-6">About Our Documentation</h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <p className="text-gray-300 mb-6">
                Our documentation provides comprehensive explanations on how to use and configure each feature of ProBot, as well as troubleshooting tips for any issues that may arise.
              </p>
              
              <h3 className="text-xl font-semibold text-white mb-4">Modules</h3>
              <p className="text-gray-300 mb-6">
                Easily navigate to a specific module by clicking its name in the sidebar on the left. Each module has its own configuration page.
              </p>
              
              <h3 className="text-xl font-semibold text-white mb-4">Commands</h3>
              <p className="text-gray-300">
                All ProBot commands have a dedicated page with usage examples and information. Check out our commands page for more details.
              </p>
            </div>
            
            <div>
              <h3 className="text-xl font-semibold text-white mb-4">Color Coding System</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-blue-500 rounded"></div>
                  <span className="text-gray-300">Indicates feature-related tips</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-green-500 rounded"></div>
                  <span className="text-gray-300">Provides feature-related information</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                  <span className="text-gray-300">Highlights feature-related cautions</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-purple-500 rounded"></div>
                  <span className="text-gray-300">Shows feature-related notes</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-red-500 rounded"></div>
                  <span className="text-gray-300">Indicates known bugs and issues to avoid</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Essential Links */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mt-12">
          <h2 className="text-3xl font-bold text-white mb-6 text-center">Essential Links</h2>
          <div className="grid md:grid-cols-3 gap-6">
            <a href="https://discord.gg/probot" className="bg-indigo-600 hover:bg-indigo-700 text-white p-4 rounded-lg text-center transition-colors">
              <FaUsers className="mx-auto mb-2 text-2xl" />
              <div className="font-semibold">Support Server</div>
            </a>
            <Link href="/commands" className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center transition-colors">
              <FaRobot className="mx-auto mb-2 text-2xl" />
              <div className="font-semibold">Commands</div>
            </Link>
            <a href="https://www.reddit.com/r/probot" className="bg-orange-600 hover:bg-orange-700 text-white p-4 rounded-lg text-center transition-colors">
              <FaBook className="mx-auto mb-2 text-2xl" />
              <div className="font-semibold">Reddit Community</div>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
