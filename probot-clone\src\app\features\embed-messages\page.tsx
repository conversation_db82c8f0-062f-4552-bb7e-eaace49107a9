import Link from 'next/link';
import { FaHome, FaCode, FaPalette, FaImage, FaLink, FaCheck, FaCog } from 'react-icons/fa';

const features = [
  {
    icon: FaPalette,
    title: 'Rich Customization',
    description: 'Customize colors, titles, descriptions, fields, and more to match your server theme',
  },
  {
    icon: FaImage,
    title: 'Images & Thumbnails',
    description: 'Add images, thumbnails, and author icons to make your embeds visually appealing',
  },
  {
    icon: FaLink,
    title: 'Interactive Elements',
    description: 'Include clickable links, timestamps, and footer information',
  },
  {
    icon: FaCode,
    title: 'Easy Builder',
    description: 'Use our intuitive embed builder with live preview functionality',
  },
];

const embedElements = [
  { name: 'Title', description: 'Main heading of your embed message' },
  { name: 'Description', description: 'Main content text of the embed' },
  { name: 'Color', description: 'Left border color of the embed' },
  { name: 'Author', description: 'Author name, icon, and URL' },
  { name: 'Thumbnail', description: 'Small image displayed on the right side' },
  { name: 'Image', description: 'Large image displayed in the embed' },
  { name: 'Fields', description: 'Additional information in field format' },
  { name: 'Footer', description: 'Footer text and icon at the bottom' },
  { name: 'Timestamp', description: 'Current date and time' },
  { name: 'URL', description: 'Makes the title clickable' },
];

export default function EmbedMessages() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 bg-black/20 backdrop-blur-sm">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          <span className="text-white font-bold text-xl">ProBot</span>
        </div>
        
        <div className="hidden md:flex items-center space-x-8">
          <Link href="/" className="text-white hover:text-blue-300 transition-colors flex items-center">
            <FaHome className="mr-2" />
            Home
          </Link>
          <Link href="/dashboard" className="text-white hover:text-blue-300 transition-colors">
            Dashboard
          </Link>
          <Link href="/docs" className="text-white hover:text-blue-300 transition-colors">
            Docs
          </Link>
        </div>

        <Link 
          href="/dashboard"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
        >
          Dashboard
        </Link>
      </nav>

      {/* Header */}
      <div className="container mx-auto px-6 py-12">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-purple-500 rounded-lg flex items-center justify-center mr-4">
              <FaCode className="text-white text-2xl" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Embed Messages
            </h1>
          </div>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            The rich embeds feature allows your bot to send customized versions of your preferred messages. Create beautiful, professional-looking messages that stand out in your Discord server.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {features.map((feature, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
              <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <feature.icon className="text-white text-xl" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
              <p className="text-gray-300 text-sm">{feature.description}</p>
            </div>
          ))}
        </div>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          {/* Embed Preview */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <h2 className="text-3xl font-bold text-white mb-6">Embed Preview</h2>
            
            {/* Sample Embed */}
            <div className="bg-gray-800 rounded-lg p-4 border-l-4 border-purple-500">
              <div className="flex items-center mb-3">
                <div className="w-6 h-6 bg-purple-500 rounded-full mr-2"></div>
                <span className="text-white text-sm font-semibold">ProBot</span>
                <span className="text-gray-400 text-xs ml-2">Today at 12:00 PM</span>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h3 className="text-purple-400 font-bold text-lg mb-2">Welcome to Our Server!</h3>
                    <p className="text-gray-300 text-sm mb-4">
                      Thank you for joining our amazing community. Here are some important things to know:
                    </p>
                    
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <h4 className="text-white font-semibold text-sm">📋 Rules</h4>
                        <p className="text-gray-400 text-xs">Check #rules channel</p>
                      </div>
                      <div>
                        <h4 className="text-white font-semibold text-sm">🎮 Gaming</h4>
                        <p className="text-gray-400 text-xs">Join voice channels</p>
                      </div>
                    </div>
                  </div>
                  <div className="w-16 h-16 bg-purple-500 rounded-lg ml-4"></div>
                </div>
                
                <div className="w-full h-32 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg mb-3"></div>
                
                <div className="flex items-center text-xs text-gray-400">
                  <div className="w-4 h-4 bg-purple-500 rounded-full mr-2"></div>
                  <span>ProBot • Today</span>
                </div>
              </div>
            </div>
          </div>

          {/* Setup Guide */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <h2 className="text-3xl font-bold text-white mb-6">How to Create Embeds</h2>
            
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white font-bold text-sm">1</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Access Embed Builder</h3>
                  <p className="text-gray-300">Go to the dashboard and navigate to the "Embed Messages" module.</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white font-bold text-sm">2</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Design Your Embed</h3>
                  <p className="text-gray-300">Use the visual editor to customize title, description, colors, and fields.</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white font-bold text-sm">3</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Preview & Test</h3>
                  <p className="text-gray-300">Preview your embed in real-time and test it in your server.</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white font-bold text-sm">4</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Send & Save</h3>
                  <p className="text-gray-300">Send your embed to any channel and save templates for future use.</p>
                </div>
              </div>
            </div>

            <div className="mt-8">
              <Link 
                href="/dashboard"
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors inline-block"
              >
                Create Embed Message
              </Link>
            </div>
          </div>
        </div>

        {/* Embed Elements */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Embed Elements</h2>
          <p className="text-gray-300 text-center mb-8">
            Customize every aspect of your embed messages with these available elements:
          </p>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {embedElements.map((element, index) => (
              <div key={index} className="bg-black/20 rounded-lg p-4 border border-white/10">
                <h3 className="text-purple-400 font-semibold mb-2">{element.name}</h3>
                <p className="text-gray-300 text-sm">{element.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Use Cases */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Popular Use Cases</h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-black/20 rounded-lg p-6 border border-white/10 text-center">
              <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">📢</span>
              </div>
              <h3 className="text-white font-semibold mb-2">Announcements</h3>
              <p className="text-gray-300 text-sm">Create professional announcements with rich formatting and images</p>
            </div>

            <div className="bg-black/20 rounded-lg p-6 border border-white/10 text-center">
              <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">📋</span>
              </div>
              <h3 className="text-white font-semibold mb-2">Server Rules</h3>
              <p className="text-gray-300 text-sm">Display server rules in an organized and visually appealing format</p>
            </div>

            <div className="bg-black/20 rounded-lg p-6 border border-white/10 text-center">
              <div className="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-xl">🎉</span>
              </div>
              <h3 className="text-white font-semibold mb-2">Events</h3>
              <p className="text-gray-300 text-sm">Promote events with detailed information and attractive designs</p>
            </div>
          </div>
        </div>

        {/* Features List */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">What You Can Do</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-white mb-4">Customization Options</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Custom colors and themes</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Rich text formatting</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Images and thumbnails</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Multiple fields support</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-white mb-4">Advanced Features</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Live preview editor</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Template saving</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Variable support</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">JSON import/export</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to create beautiful embed messages?
          </h2>
          <p className="text-gray-300 mb-8">
            Start building professional-looking messages for your Discord server today
          </p>
          <div className="flex justify-center space-x-4">
            <Link 
              href="/dashboard"
              className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Create Embed Message
            </Link>
            <Link 
              href="/docs"
              className="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Read Documentation
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
