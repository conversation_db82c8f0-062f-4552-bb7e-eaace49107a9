import Image from "next/image";
import Link from "next/link";
import { Fa<PERSON><PERSON><PERSON>, <PERSON>aTwitter, FaReddit } from "react-icons/fa";

export default function Home() {
  return (
    <div className="min-h-screen bg-[#36393f]">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 bg-[#2f3136] border-b border-[#40444b]">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-[#5865f2] rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          <span className="text-white font-bold text-xl">ProBot</span>
        </div>

        <div className="hidden md:flex items-center space-x-8">
          <Link href="#features" className="text-[#b9bbbe] hover:text-white transition-colors">
            Features
          </Link>
          <Link href="#resources" className="text-[#b9bbbe] hover:text-white transition-colors">
            Resources
          </Link>
          <Link href="/pricing" className="text-[#b9bbbe] hover:text-white transition-colors flex items-center">
            Premium
            <span className="ml-1 text-[#faa61a]">⭐</span>
          </Link>
        </div>

        <Link
          href="/dashboard"
          className="bg-[#5865f2] hover:bg-[#4752c4] text-white px-6 py-2 rounded transition-colors font-medium"
        >
          Dashboard
        </Link>
      </nav>

      {/* Hero Section */}
      <main className="container mx-auto px-6 py-20">
        <div className="text-center mb-16">
          <div className="mb-4 text-[#00d9ff] font-medium">
            New: Our Memberships Subscription
          </div>

          <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
            Make A Professional Discord Server!
          </h1>

          <p className="text-xl text-[#b9bbbe] mb-8 max-w-3xl mx-auto">
            A very customizable multipurpose bot for welcome image, In-depth logs, Social commands, Moderation and many more ...
          </p>

          <button className="bg-[#5865f2] hover:bg-[#4752c4] text-white px-8 py-4 rounded text-lg font-semibold transition-colors mb-8">
            Add To Discord
          </button>

          <div className="text-[#72767d] text-sm">
            TRUSTED BY OVER 9,000,000 DISCORD SERVERS, INCLUDING
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 gap-16 mb-20" id="features">
          {/* Welcome Messages */}
          <div className="bg-[#2f3136] rounded-lg p-8 border border-[#40444b]">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-[#57f287] rounded-lg flex items-center justify-center mr-4">
                <span className="text-white text-xl">👋</span>
              </div>
              <h3 className="text-2xl font-bold text-white">Welcome Messages</h3>
            </div>
            <h4 className="text-lg text-[#00d9ff] mb-4">Let's Welcome New Members with Style</h4>
            <p className="text-[#b9bbbe] mb-6">
              Create your own welcome images, which include the user's username and avatar, as well as a customizable background image!
            </p>
            <Link href="/features/welcome-messages" className="text-[#00d9ff] hover:text-[#5865f2] transition-colors">
              Learn more about Welcome & GoodBye →
            </Link>
          </div>

          {/* Embed Messages */}
          <div className="bg-[#2f3136] rounded-lg p-8 border border-[#40444b]">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-[#5865f2] rounded-lg flex items-center justify-center mr-4">
                <span className="text-white text-xl">📝</span>
              </div>
              <h3 className="text-2xl font-bold text-white">Embed Messages</h3>
            </div>
            <h4 className="text-lg text-[#00d9ff] mb-4">Easily create embeds for your server!</h4>
            <p className="text-[#b9bbbe] mb-6">
              Illustrate your creativity in embeds by using ProBot's simple customization and sending it to any preferred channel.
            </p>
            <Link href="/features/embed-messages" className="text-[#00d9ff] hover:text-[#5865f2] transition-colors">
              Learn more about Embed Messages →
            </Link>
          </div>

          {/* Self-Assignable Roles */}
          <div className="bg-[#2f3136] rounded-lg p-8 border border-[#40444b]">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-[#faa61a] rounded-lg flex items-center justify-center mr-4">
                <span className="text-white text-xl">🎭</span>
              </div>
              <h3 className="text-2xl font-bold text-white">Self-Assignable Roles</h3>
            </div>
            <h4 className="text-lg text-[#00d9ff] mb-4">React to the messages and get roles!</h4>
            <p className="text-[#b9bbbe] mb-6">
              Set up exclusive reaction roles & buttons, select menus, and let your members have the roles they deserve with a single click!
            </p>
            <Link href="/features/self-assignable-role" className="text-[#00d9ff] hover:text-[#5865f2] transition-colors">
              Learn more about Self-Assignable Roles →
            </Link>
          </div>

          {/* Leveling System */}
          <div className="bg-[#2f3136] rounded-lg p-8 border border-[#40444b]">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-[#fee75c] rounded-lg flex items-center justify-center mr-4">
                <span className="text-black text-xl">📈</span>
              </div>
              <h3 className="text-2xl font-bold text-white">Leveling System</h3>
            </div>
            <h4 className="text-lg text-[#00d9ff] mb-4">Reward your most Active and Engaged Members</h4>
            <p className="text-[#b9bbbe] mb-6">
              Reward active members with special level roles, privileged permissions, and channels as they reach a certain level!
            </p>
            <Link href="/features/leveling-system" className="text-[#00d9ff] hover:text-[#5865f2] transition-colors">
              Learn more about Leveling System →
            </Link>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mb-20">
          <h2 className="text-3xl font-bold text-white mb-4">
            Let ProBot take care of your Server
          </h2>
          <p className="text-[#b9bbbe] mb-8">
            Join over 9,000,000 servers using ProBot
          </p>
          <button className="bg-[#5865f2] hover:bg-[#4752c4] text-white px-8 py-4 rounded text-lg font-semibold transition-colors">
            Add To Discord
          </button>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-[#2f3136] border-t border-[#40444b]">
        <div className="container mx-auto px-6 py-12">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-[#5865f2] rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">P</span>
                </div>
                <span className="text-white font-bold text-xl">ProBot</span>
              </div>
              <p className="text-[#b9bbbe] text-sm">
                A very customizable multipurpose bot for welcome image, In-depth logs, Social commands, Moderation and many more ...
              </p>
            </div>

            <div>
              <h4 className="text-white font-semibold mb-4">Website Pages</h4>
              <div className="space-y-2">
                <Link href="/memberships" className="block text-[#b9bbbe] hover:text-white transition-colors text-sm">
                  Membership <span className="text-xs bg-[#5865f2] px-1 rounded">NEW</span>
                </Link>
                <Link href="/dashboard" className="block text-[#b9bbbe] hover:text-white transition-colors text-sm">Dashboard</Link>
                <Link href="/docs" className="block text-[#b9bbbe] hover:text-white transition-colors text-sm">Docs</Link>
                <Link href="/pricing" className="block text-[#b9bbbe] hover:text-white transition-colors text-sm">Premium</Link>
                <Link href="/commands" className="block text-[#b9bbbe] hover:text-white transition-colors text-sm">Commands</Link>
              </div>
            </div>

            <div>
              <h4 className="text-white font-semibold mb-4">Other Links</h4>
              <div className="space-y-2">
                <a href="https://twitter.com/TryProBot" className="block text-[#b9bbbe] hover:text-white transition-colors text-sm">Twitter</a>
                <a href="https://discord.gg/ProBot" className="block text-[#b9bbbe] hover:text-white transition-colors text-sm">Discord</a>
                <a href="https://top.gg/bot/probot" className="block text-[#b9bbbe] hover:text-white transition-colors text-sm">Top.gg</a>
              </div>
            </div>

            <div>
              <h4 className="text-white font-semibold mb-4">Rules</h4>
              <div className="space-y-2">
                <Link href="/terms-of-use" className="block text-[#b9bbbe] hover:text-white transition-colors text-sm">Terms Of Use</Link>
                <Link href="/privacy-policy" className="block text-[#b9bbbe] hover:text-white transition-colors text-sm">Privacy Policy</Link>
                <Link href="/refund-policy" className="block text-[#b9bbbe] hover:text-white transition-colors text-sm">Refund Policy</Link>
              </div>
            </div>
          </div>

          <div className="border-t border-[#40444b] mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-[#72767d] text-sm">© 2025 All rights reserved.</p>
            <div className="flex space-x-4 mt-4 md:mt-0">
              <a href="https://discord.gg/probot" className="text-[#b9bbbe] hover:text-white transition-colors">
                <FaDiscord size={20} />
              </a>
              <a href="https://www.reddit.com/r/probot" className="text-[#b9bbbe] hover:text-white transition-colors">
                <FaReddit size={20} />
              </a>
              <a href="https://twitter.com/TryProBot" className="text-[#b9bbbe] hover:text-white transition-colors">
                <FaTwitter size={20} />
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
