import Link from 'next/link';
import { FaHome, FaChartLine, FaTrophy, FaGift, FaUsers, FaCheck, FaCog, FaStar } from 'react-icons/fa';

const features = [
  {
    icon: FaChartLine,
    title: 'XP & Levels',
    description: 'Members gain XP for activity and level up automatically',
  },
  {
    icon: FaTrophy,
    title: 'Level Roles',
    description: 'Reward members with special roles as they reach higher levels',
  },
  {
    icon: FaGift,
    title: 'Rewards System',
    description: 'Give access to exclusive channels and permissions',
  },
  {
    icon: FaUsers,
    title: 'Leaderboards',
    description: 'Show top members and encourage healthy competition',
  },
];

const levelRewards = [
  { level: 5, reward: 'Trusted Member', color: 'bg-green-500', description: 'Access to trusted channels' },
  { level: 10, reward: 'Active Member', color: 'bg-blue-500', description: 'Special permissions and channels' },
  { level: 25, reward: 'Veteran', color: 'bg-purple-500', description: 'Veteran status and exclusive perks' },
  { level: 50, reward: 'Elite', color: 'bg-yellow-500', description: 'Elite role with maximum privileges' },
  { level: 100, reward: 'Legend', color: 'bg-red-500', description: 'Legendary status and recognition' },
];

export default function LevelingSystem() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 bg-black/20 backdrop-blur-sm">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          <span className="text-white font-bold text-xl">ProBot</span>
        </div>
        
        <div className="hidden md:flex items-center space-x-8">
          <Link href="/" className="text-white hover:text-blue-300 transition-colors flex items-center">
            <FaHome className="mr-2" />
            Home
          </Link>
          <Link href="/dashboard" className="text-white hover:text-blue-300 transition-colors">
            Dashboard
          </Link>
          <Link href="/docs" className="text-white hover:text-blue-300 transition-colors">
            Docs
          </Link>
        </div>

        <Link 
          href="/dashboard"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
        >
          Dashboard
        </Link>
      </nav>

      {/* Header */}
      <div className="container mx-auto px-6 py-12">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-yellow-500 rounded-lg flex items-center justify-center mr-4">
              <FaChartLine className="text-white text-2xl" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Leveling System
            </h1>
          </div>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            With ProBot levelling system, active server members will be automatically rewarded with extraordinary level roles, privileged permissions, and access to mystery channels once they reach a specific level!
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {features.map((feature, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
              <div className="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <feature.icon className="text-white text-xl" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
              <p className="text-gray-300 text-sm">{feature.description}</p>
            </div>
          ))}
        </div>

        {/* How It Works */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">How the Leveling System Works</h2>
          
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Earn XP</h3>
              <p className="text-gray-300">
                Members earn XP by sending messages, participating in voice channels, and being active in the server.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Level Up</h3>
              <p className="text-gray-300">
                When members accumulate enough XP, they automatically level up and receive notifications.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Get Rewards</h3>
              <p className="text-gray-300">
                Receive special roles, permissions, and access to exclusive channels based on your level.
              </p>
            </div>
          </div>
        </div>

        {/* Level Rewards */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Example Level Rewards</h2>
          
          <div className="space-y-4">
            {levelRewards.map((reward, index) => (
              <div key={index} className="bg-black/20 rounded-lg p-6 border border-white/10 flex items-center">
                <div className={`w-16 h-16 ${reward.color} rounded-lg flex items-center justify-center mr-6`}>
                  <span className="text-white font-bold text-lg">{reward.level}</span>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-white mb-1">{reward.reward}</h3>
                  <p className="text-gray-300">{reward.description}</p>
                </div>
                <div className="text-right">
                  <span className="text-gray-400 text-sm">Level {reward.level}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Leaderboard Preview */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Server Leaderboard</h2>
          
          <div className="bg-black/20 rounded-lg p-6 border border-white/10">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-yellow-600/20 rounded-lg border border-yellow-500/30">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-bold">1</span>
                  </div>
                  <div className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mr-4"></div>
                  <div>
                    <h4 className="text-white font-semibold">SuperUser#1234</h4>
                    <p className="text-gray-300 text-sm">Level 87 • Legend</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-semibold">125,430 XP</p>
                  <p className="text-gray-400 text-sm">2,340 messages</p>
                </div>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-600/20 rounded-lg">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-bold">2</span>
                  </div>
                  <div className="w-10 h-10 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full mr-4"></div>
                  <div>
                    <h4 className="text-white font-semibold">ActiveMember#5678</h4>
                    <p className="text-gray-300 text-sm">Level 65 • Elite</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-semibold">98,750 XP</p>
                  <p className="text-gray-400 text-sm">1,890 messages</p>
                </div>
              </div>

              <div className="flex items-center justify-between p-4 bg-amber-600/20 rounded-lg">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-bold">3</span>
                  </div>
                  <div className="w-10 h-10 bg-gradient-to-r from-amber-400 to-amber-600 rounded-full mr-4"></div>
                  <div>
                    <h4 className="text-white font-semibold">ChattyUser#9012</h4>
                    <p className="text-gray-300 text-sm">Level 52 • Veteran</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-semibold">76,320 XP</p>
                  <p className="text-gray-400 text-sm">1,520 messages</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Configuration Options */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Configuration Options</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-white mb-4">XP Settings</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Custom XP per message</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Voice channel XP rates</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">XP cooldown periods</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Channel XP multipliers</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-white mb-4">Level Rewards</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Automatic role assignment</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Custom level-up messages</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Channel access rewards</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Permission-based rewards</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Commands */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Level Commands</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-black/20 rounded-lg p-4 border border-white/10">
              <code className="text-blue-300 font-mono font-semibold">/rank [@user]</code>
              <p className="text-gray-300 text-sm mt-2">Check your rank or someone else's rank and level progress</p>
            </div>

            <div className="bg-black/20 rounded-lg p-4 border border-white/10">
              <code className="text-blue-300 font-mono font-semibold">/leaderboard</code>
              <p className="text-gray-300 text-sm mt-2">Display the server leaderboard with top members</p>
            </div>

            <div className="bg-black/20 rounded-lg p-4 border border-white/10">
              <code className="text-blue-300 font-mono font-semibold">/level-roles setup</code>
              <p className="text-gray-300 text-sm mt-2">Configure level roles and rewards for your server</p>
            </div>

            <div className="bg-black/20 rounded-lg p-4 border border-white/10">
              <code className="text-blue-300 font-mono font-semibold">/xp add @user [amount]</code>
              <p className="text-gray-300 text-sm mt-2">Manually add XP to a user (Admin only)</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to reward your active members?
          </h2>
          <p className="text-gray-300 mb-8">
            Set up the leveling system and start recognizing your most engaged community members
          </p>
          <div className="flex justify-center space-x-4">
            <Link 
              href="/dashboard"
              className="bg-yellow-600 hover:bg-yellow-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Configure Leveling System
            </Link>
            <Link 
              href="/docs"
              className="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Read Documentation
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
