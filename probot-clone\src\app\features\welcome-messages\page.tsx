import Link from 'next/link';
import Image from 'next/image';
import { FaHome, FaUsers, FaImage, FaCog, FaCheck, FaPalette, FaCode } from 'react-icons/fa';

const features = [
  {
    icon: FaImage,
    title: 'Custom Welcome Images',
    description: 'Create beautiful welcome images with user avatars, usernames, and custom backgrounds',
  },
  {
    icon: FaPalette,
    title: 'Customizable Design',
    description: 'Choose from multiple templates or create your own design with custom colors and fonts',
  },
  {
    icon: FaCode,
    title: 'Dynamic Variables',
    description: 'Use variables like {user}, {server}, {membercount} to personalize messages',
  },
  {
    icon: FaCog,
    title: 'Easy Setup',
    description: 'Configure everything through our intuitive web dashboard in just a few clicks',
  },
];

const variables = [
  { name: '{user}', description: 'Mentions the new member' },
  { name: '{username}', description: 'Shows the username without mention' },
  { name: '{server}', description: 'Shows the server name' },
  { name: '{membercount}', description: 'Shows current member count' },
  { name: '{user.id}', description: 'Shows the user ID' },
  { name: '{user.avatar}', description: 'Shows the user avatar URL' },
  { name: '{user.tag}', description: 'Shows username#discriminator' },
  { name: '{date}', description: 'Shows current date' },
];

export default function WelcomeMessages() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 bg-black/20 backdrop-blur-sm">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          <span className="text-white font-bold text-xl">ProBot</span>
        </div>
        
        <div className="hidden md:flex items-center space-x-8">
          <Link href="/" className="text-white hover:text-blue-300 transition-colors flex items-center">
            <FaHome className="mr-2" />
            Home
          </Link>
          <Link href="/dashboard" className="text-white hover:text-blue-300 transition-colors">
            Dashboard
          </Link>
          <Link href="/docs" className="text-white hover:text-blue-300 transition-colors">
            Docs
          </Link>
        </div>

        <Link 
          href="/dashboard"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
        >
          Dashboard
        </Link>
      </nav>

      {/* Header */}
      <div className="container mx-auto px-6 py-12">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-green-500 rounded-lg flex items-center justify-center mr-4">
              <FaUsers className="text-white text-2xl" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Welcome Messages
            </h1>
          </div>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Welcome and goodbye messages play a vital role in establishing a welcoming and engaging community on your Discord server. Create custom welcome images and messages that make new members feel valued.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {features.map((feature, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
              <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <feature.icon className="text-white text-xl" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
              <p className="text-gray-300 text-sm">{feature.description}</p>
            </div>
          ))}
        </div>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          {/* Setup Guide */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <h2 className="text-3xl font-bold text-white mb-6">How to Setup Welcome Messages</h2>
            
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white font-bold text-sm">1</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Access Dashboard</h3>
                  <p className="text-gray-300">Go to the ProBot dashboard and select your server from the dropdown menu.</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white font-bold text-sm">2</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Navigate to Welcome Module</h3>
                  <p className="text-gray-300">Click on "Welcome & Goodbye" in the modules section of the sidebar.</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white font-bold text-sm">3</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Configure Settings</h3>
                  <p className="text-gray-300">Set your welcome channel, customize your message, and enable welcome images.</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white font-bold text-sm">4</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Test & Save</h3>
                  <p className="text-gray-300">Test your welcome message and save your configuration.</p>
                </div>
              </div>
            </div>

            <div className="mt-8">
              <Link 
                href="/dashboard"
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors inline-block"
              >
                Setup Welcome Messages
              </Link>
            </div>
          </div>

          {/* Variables Reference */}
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <h2 className="text-3xl font-bold text-white mb-6">Available Variables</h2>
            <p className="text-gray-300 mb-6">
              Use these dynamic variables in your welcome messages to personalize them for each new member:
            </p>
            
            <div className="space-y-4">
              {variables.map((variable, index) => (
                <div key={index} className="bg-black/20 rounded-lg p-4 border border-white/10">
                  <div className="flex items-center justify-between">
                    <code className="text-green-400 font-mono font-semibold">
                      {variable.name}
                    </code>
                  </div>
                  <p className="text-gray-300 text-sm mt-2">
                    {variable.description}
                  </p>
                </div>
              ))}
            </div>

            <div className="mt-6 p-4 bg-blue-600/20 rounded-lg border border-blue-500/30">
              <h4 className="text-blue-300 font-semibold mb-2">Example Message:</h4>
              <code className="text-gray-300 text-sm">
                Welcome {'{user}'} to {'{server}'}! You are our {'{membercount}'}th member. Enjoy your stay! 🎉
              </code>
            </div>
          </div>
        </div>

        {/* Image Examples */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Welcome Image Examples</h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-black/20 rounded-lg p-4 border border-white/10">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg h-32 mb-4 flex items-center justify-center">
                <span className="text-white font-bold">Template 1</span>
              </div>
              <h3 className="text-white font-semibold mb-2">Modern Design</h3>
              <p className="text-gray-300 text-sm">Clean and modern welcome image with gradient background</p>
            </div>

            <div className="bg-black/20 rounded-lg p-4 border border-white/10">
              <div className="bg-gradient-to-r from-green-500 to-teal-600 rounded-lg h-32 mb-4 flex items-center justify-center">
                <span className="text-white font-bold">Template 2</span>
              </div>
              <h3 className="text-white font-semibold mb-2">Gaming Theme</h3>
              <p className="text-gray-300 text-sm">Perfect for gaming communities with vibrant colors</p>
            </div>

            <div className="bg-black/20 rounded-lg p-4 border border-white/10">
              <div className="bg-gradient-to-r from-pink-500 to-red-600 rounded-lg h-32 mb-4 flex items-center justify-center">
                <span className="text-white font-bold">Template 3</span>
              </div>
              <h3 className="text-white font-semibold mb-2">Elegant Style</h3>
              <p className="text-gray-300 text-sm">Sophisticated design for professional communities</p>
            </div>
          </div>
        </div>

        {/* Features List */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">What You Can Do</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-white mb-4">Welcome Messages</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Custom welcome channel selection</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Personalized welcome messages</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Custom welcome images</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Dynamic variables support</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-white mb-4">Goodbye Messages</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Custom goodbye channel selection</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Personalized goodbye messages</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Member statistics tracking</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Customizable message format</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to welcome new members in style?
          </h2>
          <p className="text-gray-300 mb-8">
            Set up custom welcome messages and images for your Discord server today
          </p>
          <div className="flex justify-center space-x-4">
            <Link 
              href="/dashboard"
              className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Configure Welcome Messages
            </Link>
            <Link 
              href="/docs"
              className="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Read Documentation
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
