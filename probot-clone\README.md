# ProBot Clone - Complete Discord Bot Website

A complete replica of the ProBot Discord bot website with all features, pages, and functionality. Built with Next.js, TypeScript, and Tailwind CSS.

## 🌟 Features

### 🏠 Main Pages
- **Homepage** - Complete landing page with hero section, features showcase, and footer
- **Dashboard** - Full-featured control panel with all 15 modules
- **Commands** - Comprehensive command documentation
- **Pricing** - Premium subscription plans and comparison
- **Documentation** - Complete docs with all modules and guides
- **Memberships** - New membership tiers and benefits

### 🔧 Bot Modules (15 Total)
1. **Welcome & Goodbye** - Custom welcome messages and images
2. **Auto Responder** - Automatic response system
3. **Embed Messages** - Rich embed message creator
4. **Level System** - XP and leveling with rewards
5. **Auto-Roles** - Automatic role assignment
6. **Logs** - Server activity monitoring
7. **Colors** - Color role selection
8. **Self-Assignable Roles** - Reaction, button, and dropdown roles
9. **Temporary Channels** - Dynamic voice channels
10. **Temp Links** - Temporary invite links
11. **Voice Online** - Voice activity display
12. **Anti-Raid** - Server protection (Premium)
13. **VIP Protection** - Advanced protection (Premium)
14. **Starboard** - Popular message highlighting
15. **Automod** - Automatic moderation

### 📄 Feature Pages
- **Welcome Messages** - Detailed feature explanation and setup guide
- **Embed Messages** - Embed builder documentation
- **Self-Assignable Roles** - Role assignment system guide
- **Leveling System** - XP and rewards documentation

### 📋 Legal Pages
- **Terms of Use** - Complete terms and conditions
- **Privacy Policy** - Comprehensive privacy policy
- **Refund Policy** - Detailed refund terms and process

## 🚀 Tech Stack

- **Framework**: Next.js 15.3.3 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Icons**: React Icons
- **Animations**: Framer Motion
- **Database**: Supabase (configured)

## 🎨 Design Features

- **Discord Color Scheme** - Authentic Discord-inspired design
- **Responsive Design** - Mobile-first approach
- **Smooth Animations** - Enhanced user experience
- **Eye-friendly Colors** - Optimized for long viewing sessions
- **Modern UI Components** - Professional and polished interface

## 🛠️ Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd probot-clone
```

2. **Install dependencies**
```bash
npm install
```

3. **Run the development server**
```bash
npm run dev
```

4. **Open in browser**
Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
probot-clone/
├── src/
│   └── app/
│       ├── page.tsx                    # Homepage
│       ├── dashboard/
│       │   └── page.tsx               # Dashboard
│       ├── commands/
│       │   └── page.tsx               # Commands page
│       ├── pricing/
│       │   └── page.tsx               # Pricing page
│       ├── docs/
│       │   └── page.tsx               # Documentation
│       ├── memberships/
│       │   └── page.tsx               # Memberships
│       ├── features/
│       │   ├── welcome-messages/
│       │   ├── embed-messages/
│       │   ├── self-assignable-role/
│       │   └── leveling-system/
│       ├── terms-of-use/
│       ├── privacy-policy/
│       └── refund-policy/
├── public/                            # Static assets
└── README.md
```

## 🎯 Key Features Implemented

### Dashboard Functionality
- **Server Selection** - Dropdown for multiple servers
- **Module Navigation** - Sidebar with all 15 modules
- **Welcome Module** - Fully functional configuration interface
- **Responsive Design** - Works on all device sizes

### Homepage Features
- **Hero Section** - Compelling call-to-action
- **Feature Showcase** - 4 main features with descriptions
- **Trusted By Section** - Social proof
- **Complete Footer** - All links and information

### Navigation
- **Consistent Header** - Present on all pages
- **Breadcrumb Navigation** - Easy page navigation
- **Mobile Menu** - Responsive navigation

## 🔮 Future Enhancements

- **Backend Integration** - Connect to actual Discord API
- **User Authentication** - Discord OAuth integration
- **Database Integration** - Store user configurations
- **Real-time Updates** - Live dashboard updates
- **Payment Integration** - Stripe for premium subscriptions

## 📱 Responsive Design

The website is fully responsive and optimized for:
- **Desktop** - Full feature experience
- **Tablet** - Adapted layouts
- **Mobile** - Touch-friendly interface

## 🎨 Color Scheme

Following Discord's design principles:
- **Primary**: Indigo/Purple gradients
- **Secondary**: Blue accents
- **Success**: Green highlights
- **Warning**: Yellow/Orange alerts
- **Error**: Red indicators

## 🚀 Deployment

The project is ready for deployment on:
- **Vercel** (Recommended)
- **Netlify**
- **Railway**
- **Any Node.js hosting platform**

## 📄 License

This project is for educational purposes and demonstrates web development skills. It replicates the ProBot website design and functionality.

## 🤝 Contributing

This is a demonstration project. For actual ProBot development, visit the official ProBot website.

---

**Built with ❤️ using Next.js and TypeScript**
