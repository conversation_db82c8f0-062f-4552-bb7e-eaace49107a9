import Link from 'next/link';
import { FaHome, FaFileContract } from 'react-icons/fa';

export default function TermsOfUse() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 bg-black/20 backdrop-blur-sm">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          <span className="text-white font-bold text-xl">ProBot</span>
        </div>
        
        <div className="hidden md:flex items-center space-x-8">
          <Link href="/" className="text-white hover:text-blue-300 transition-colors flex items-center">
            <FaHome className="mr-2" />
            Home
          </Link>
          <Link href="/dashboard" className="text-white hover:text-blue-300 transition-colors">
            Dashboard
          </Link>
          <Link href="/docs" className="text-white hover:text-blue-300 transition-colors">
            Docs
          </Link>
        </div>

        <Link 
          href="/dashboard"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
        >
          Dashboard
        </Link>
      </nav>

      {/* Header */}
      <div className="container mx-auto px-6 py-12">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
              <FaFileContract className="text-white text-2xl" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Terms of Use
            </h1>
          </div>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Please read these Terms of Use carefully before using ProBot services.
          </p>
          <p className="text-gray-400 text-sm">
            Last updated: January 1, 2025
          </p>
        </div>

        {/* Content */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <div className="prose prose-invert max-w-none">
              
              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">1. Acceptance of Terms</h2>
                <p className="text-gray-300 mb-4">
                  By accessing and using ProBot ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">2. Description of Service</h2>
                <p className="text-gray-300 mb-4">
                  ProBot is a Discord bot service that provides various features including but not limited to:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Welcome and goodbye messages</li>
                  <li>Moderation tools</li>
                  <li>Level and ranking systems</li>
                  <li>Self-assignable roles</li>
                  <li>Server logs and analytics</li>
                  <li>Custom commands and automation</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">3. User Responsibilities</h2>
                <p className="text-gray-300 mb-4">
                  As a user of ProBot, you agree to:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Use the service in compliance with Discord's Terms of Service</li>
                  <li>Not use the service for illegal or harmful activities</li>
                  <li>Not attempt to exploit, hack, or abuse the service</li>
                  <li>Respect other users and maintain appropriate conduct</li>
                  <li>Not spam or abuse the bot's features</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">4. Premium Services</h2>
                <p className="text-gray-300 mb-4">
                  ProBot offers premium subscription services with additional features. Premium subscriptions:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Are billed monthly or annually as selected</li>
                  <li>Automatically renew unless cancelled</li>
                  <li>Can be cancelled at any time</li>
                  <li>Provide access to premium features during the subscription period</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">5. Data Collection and Privacy</h2>
                <p className="text-gray-300 mb-4">
                  ProBot collects and processes data necessary to provide its services. This includes:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Discord user IDs and server information</li>
                  <li>Message content for moderation and leveling features</li>
                  <li>Configuration settings and preferences</li>
                  <li>Usage statistics and analytics</li>
                </ul>
                <p className="text-gray-300 mb-4">
                  For detailed information about data handling, please refer to our Privacy Policy.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">6. Service Availability</h2>
                <p className="text-gray-300 mb-4">
                  While we strive to maintain high availability, ProBot services may experience:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Scheduled maintenance downtime</li>
                  <li>Temporary service interruptions</li>
                  <li>Feature updates and changes</li>
                </ul>
                <p className="text-gray-300 mb-4">
                  We do not guarantee 100% uptime and are not liable for service interruptions.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">7. Limitation of Liability</h2>
                <p className="text-gray-300 mb-4">
                  ProBot and its operators shall not be liable for any direct, indirect, incidental, special, or consequential damages resulting from the use or inability to use the service.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">8. Termination</h2>
                <p className="text-gray-300 mb-4">
                  We reserve the right to terminate or suspend access to our service immediately, without prior notice, for any reason, including but not limited to breach of these Terms.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">9. Changes to Terms</h2>
                <p className="text-gray-300 mb-4">
                  We reserve the right to modify these terms at any time. Changes will be effective immediately upon posting. Continued use of the service constitutes acceptance of modified terms.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">10. Contact Information</h2>
                <p className="text-gray-300 mb-4">
                  If you have any questions about these Terms of Use, please contact us through:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Discord Support Server: <a href="https://discord.gg/ProBot" className="text-blue-400 hover:text-blue-300">discord.gg/ProBot</a></li>
                  <li>Website: <a href="https://probot.io" className="text-blue-400 hover:text-blue-300">probot.io</a></li>
                </ul>
              </section>

            </div>
          </div>

          {/* Navigation Links */}
          <div className="mt-12 text-center">
            <div className="flex justify-center space-x-4">
              <Link 
                href="/privacy-policy"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                Privacy Policy
              </Link>
              <Link 
                href="/refund-policy"
                className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                Refund Policy
              </Link>
              <Link 
                href="/"
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
