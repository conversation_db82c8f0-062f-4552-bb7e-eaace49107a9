import Link from 'next/link';
import { FaHome, FaShieldAlt } from 'react-icons/fa';

export default function PrivacyPolicy() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 bg-black/20 backdrop-blur-sm">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          <span className="text-white font-bold text-xl">ProBot</span>
        </div>
        
        <div className="hidden md:flex items-center space-x-8">
          <Link href="/" className="text-white hover:text-blue-300 transition-colors flex items-center">
            <FaHome className="mr-2" />
            Home
          </Link>
          <Link href="/dashboard" className="text-white hover:text-blue-300 transition-colors">
            Dashboard
          </Link>
          <Link href="/docs" className="text-white hover:text-blue-300 transition-colors">
            Docs
          </Link>
        </div>

        <Link 
          href="/dashboard"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
        >
          Dashboard
        </Link>
      </nav>

      {/* Header */}
      <div className="container mx-auto px-6 py-12">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-green-500 rounded-lg flex items-center justify-center mr-4">
              <FaShieldAlt className="text-white text-2xl" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Privacy Policy
            </h1>
          </div>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Your privacy is important to us. This policy explains how we collect, use, and protect your information.
          </p>
          <p className="text-gray-400 text-sm">
            Last updated: January 1, 2025
          </p>
        </div>

        {/* Content */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <div className="prose prose-invert max-w-none">
              
              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">1. Information We Collect</h2>
                <p className="text-gray-300 mb-4">
                  ProBot collects the following types of information to provide and improve our services:
                </p>
                
                <h3 className="text-xl font-semibold text-white mb-3">Discord Data</h3>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>User IDs, usernames, and discriminators</li>
                  <li>Server (guild) IDs and names</li>
                  <li>Channel IDs and names</li>
                  <li>Role information and permissions</li>
                  <li>Message content for moderation and leveling features</li>
                </ul>

                <h3 className="text-xl font-semibold text-white mb-3">Usage Data</h3>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Command usage statistics</li>
                  <li>Feature utilization metrics</li>
                  <li>Error logs and performance data</li>
                  <li>Dashboard access and configuration changes</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">2. How We Use Your Information</h2>
                <p className="text-gray-300 mb-4">
                  We use the collected information for the following purposes:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Providing and maintaining ProBot services</li>
                  <li>Processing commands and executing bot functions</li>
                  <li>Generating welcome messages and server logs</li>
                  <li>Managing leveling systems and role assignments</li>
                  <li>Improving service quality and performance</li>
                  <li>Providing customer support</li>
                  <li>Detecting and preventing abuse or violations</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">3. Data Storage and Security</h2>
                <p className="text-gray-300 mb-4">
                  We implement appropriate security measures to protect your information:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Data is stored on secure servers with encryption</li>
                  <li>Access to data is limited to authorized personnel only</li>
                  <li>Regular security audits and updates are performed</li>
                  <li>Data backups are encrypted and securely stored</li>
                </ul>
                <p className="text-gray-300 mb-4">
                  However, no method of transmission over the internet is 100% secure, and we cannot guarantee absolute security.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">4. Data Sharing and Disclosure</h2>
                <p className="text-gray-300 mb-4">
                  We do not sell, trade, or rent your personal information to third parties. We may share information in the following circumstances:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>With your explicit consent</li>
                  <li>To comply with legal obligations or court orders</li>
                  <li>To protect our rights, property, or safety</li>
                  <li>In connection with a business transfer or merger</li>
                  <li>With service providers who assist in our operations (under strict confidentiality agreements)</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">5. Data Retention</h2>
                <p className="text-gray-300 mb-4">
                  We retain your information for as long as necessary to provide our services:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Active server data is retained while the bot is in your server</li>
                  <li>User level and XP data is retained for service continuity</li>
                  <li>Configuration settings are stored until manually deleted</li>
                  <li>Logs and analytics data may be retained for up to 90 days</li>
                  <li>Premium subscription data is retained as required by law</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">6. Your Rights and Choices</h2>
                <p className="text-gray-300 mb-4">
                  You have the following rights regarding your data:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Access: Request information about data we have collected</li>
                  <li>Correction: Request correction of inaccurate data</li>
                  <li>Deletion: Request deletion of your data (subject to legal requirements)</li>
                  <li>Portability: Request a copy of your data in a portable format</li>
                  <li>Opt-out: Disable certain features or data collection</li>
                </ul>
                <p className="text-gray-300 mb-4">
                  To exercise these rights, please contact us through our support channels.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">7. Children's Privacy</h2>
                <p className="text-gray-300 mb-4">
                  ProBot is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13. If we become aware that we have collected such information, we will take steps to delete it promptly.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">8. International Data Transfers</h2>
                <p className="text-gray-300 mb-4">
                  Your information may be transferred to and processed in countries other than your own. We ensure that such transfers comply with applicable data protection laws and implement appropriate safeguards.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">9. Changes to This Policy</h2>
                <p className="text-gray-300 mb-4">
                  We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new policy on this page and updating the "Last updated" date. Continued use of our services after changes constitutes acceptance of the updated policy.
                </p>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">10. Contact Us</h2>
                <p className="text-gray-300 mb-4">
                  If you have any questions about this Privacy Policy or our data practices, please contact us:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Discord Support Server: <a href="https://discord.gg/ProBot" className="text-blue-400 hover:text-blue-300">discord.gg/ProBot</a></li>
                  <li>Website: <a href="https://probot.io" className="text-blue-400 hover:text-blue-300">probot.io</a></li>
                  <li>Email: <EMAIL></li>
                </ul>
              </section>

            </div>
          </div>

          {/* Navigation Links */}
          <div className="mt-12 text-center">
            <div className="flex justify-center space-x-4">
              <Link 
                href="/terms-of-use"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                Terms of Use
              </Link>
              <Link 
                href="/refund-policy"
                className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                Refund Policy
              </Link>
              <Link 
                href="/"
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
