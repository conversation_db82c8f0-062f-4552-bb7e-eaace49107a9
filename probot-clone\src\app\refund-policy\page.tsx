import Link from 'next/link';
import { FaHome, FaMoneyBillWave } from 'react-icons/fa';

export default function RefundPolicy() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 bg-black/20 backdrop-blur-sm">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          <span className="text-white font-bold text-xl">ProBot</span>
        </div>
        
        <div className="hidden md:flex items-center space-x-8">
          <Link href="/" className="text-white hover:text-blue-300 transition-colors flex items-center">
            <FaHome className="mr-2" />
            Home
          </Link>
          <Link href="/dashboard" className="text-white hover:text-blue-300 transition-colors">
            Dashboard
          </Link>
          <Link href="/docs" className="text-white hover:text-blue-300 transition-colors">
            Docs
          </Link>
        </div>

        <Link 
          href="/dashboard"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
        >
          Dashboard
        </Link>
      </nav>

      {/* Header */}
      <div className="container mx-auto px-6 py-12">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-yellow-500 rounded-lg flex items-center justify-center mr-4">
              <FaMoneyBillWave className="text-white text-2xl" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Refund Policy
            </h1>
          </div>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            We want you to be satisfied with ProBot Premium. Here's our refund policy for premium subscriptions.
          </p>
          <p className="text-gray-400 text-sm">
            Last updated: January 1, 2025
          </p>
        </div>

        {/* Content */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <div className="prose prose-invert max-w-none">
              
              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">1. 7-Day Money-Back Guarantee</h2>
                <p className="text-gray-300 mb-4">
                  We offer a 7-day money-back guarantee for all new premium subscriptions. If you're not satisfied with ProBot Premium within the first 7 days of your subscription, you can request a full refund.
                </p>
                <div className="bg-green-600/20 rounded-lg p-4 border border-green-500/30 mb-4">
                  <h4 className="text-green-300 font-semibold mb-2">✅ Eligible for Full Refund</h4>
                  <ul className="list-disc list-inside text-gray-300 space-y-1">
                    <li>First-time premium subscribers</li>
                    <li>Requests made within 7 days of initial purchase</li>
                    <li>No previous refund requests for the same account</li>
                  </ul>
                </div>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">2. Refund Eligibility</h2>
                <p className="text-gray-300 mb-4">
                  Refunds are available under the following conditions:
                </p>
                
                <h3 className="text-xl font-semibold text-white mb-3">Eligible Situations</h3>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Technical issues preventing service usage that cannot be resolved</li>
                  <li>Billing errors or duplicate charges</li>
                  <li>Service not delivered as described</li>
                  <li>Accidental purchases (within 24 hours)</li>
                  <li>Subscription cancellation within the 7-day guarantee period</li>
                </ul>

                <h3 className="text-xl font-semibold text-white mb-3">Non-Eligible Situations</h3>
                <div className="bg-red-600/20 rounded-lg p-4 border border-red-500/30 mb-4">
                  <ul className="list-disc list-inside text-gray-300 space-y-2">
                    <li>Change of mind after the 7-day period</li>
                    <li>Failure to use the service</li>
                    <li>Violation of Terms of Service resulting in suspension</li>
                    <li>Partial month refunds for monthly subscriptions</li>
                    <li>Refunds for gift subscriptions (unless technical issues)</li>
                  </ul>
                </div>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">3. Refund Process</h2>
                <p className="text-gray-300 mb-4">
                  To request a refund, please follow these steps:
                </p>
                
                <div className="space-y-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-yellow-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-white font-bold text-sm">1</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white mb-2">Contact Support</h4>
                      <p className="text-gray-300">Join our Discord support server or send us a message through the dashboard.</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-yellow-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-white font-bold text-sm">2</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white mb-2">Provide Information</h4>
                      <p className="text-gray-300">Include your Discord ID, server ID, subscription details, and reason for refund.</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-yellow-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-white font-bold text-sm">3</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white mb-2">Review Process</h4>
                      <p className="text-gray-300">Our team will review your request within 2-3 business days.</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-yellow-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <span className="text-white font-bold text-sm">4</span>
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-white mb-2">Refund Processing</h4>
                      <p className="text-gray-300">If approved, refunds are processed within 5-10 business days to your original payment method.</p>
                    </div>
                  </div>
                </div>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">4. Subscription Cancellation</h2>
                <p className="text-gray-300 mb-4">
                  You can cancel your subscription at any time:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Cancellation takes effect at the end of your current billing period</li>
                  <li>You retain access to premium features until the subscription expires</li>
                  <li>No refund is provided for the remaining subscription period (except within 7-day guarantee)</li>
                  <li>You can reactivate your subscription at any time</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">5. Billing Issues</h2>
                <p className="text-gray-300 mb-4">
                  If you experience billing problems:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Duplicate charges will be refunded immediately</li>
                  <li>Failed payments may result in service suspension</li>
                  <li>Contact support for billing disputes or questions</li>
                  <li>Update payment methods through the dashboard</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">6. Chargebacks and Disputes</h2>
                <div className="bg-yellow-600/20 rounded-lg p-4 border border-yellow-500/30 mb-4">
                  <h4 className="text-yellow-300 font-semibold mb-2">⚠️ Important Notice</h4>
                  <p className="text-gray-300">
                    Please contact our support team before initiating a chargeback with your bank or credit card company. 
                    Chargebacks may result in permanent account suspension and loss of access to all services.
                  </p>
                </div>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">7. Gift Subscriptions</h2>
                <p className="text-gray-300 mb-4">
                  Special conditions apply to gift subscriptions:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Gift purchasers can request refunds within 7 days if unused</li>
                  <li>Recipients cannot request refunds on behalf of gift purchasers</li>
                  <li>Technical issues affecting gift subscriptions are eligible for refunds</li>
                  <li>Unused gift subscriptions may be refunded at our discretion</li>
                </ul>
              </section>

              <section className="mb-8">
                <h2 className="text-2xl font-bold text-white mb-4">8. Contact Information</h2>
                <p className="text-gray-300 mb-4">
                  For refund requests or billing questions, contact us through:
                </p>
                <ul className="list-disc list-inside text-gray-300 mb-4 space-y-2">
                  <li>Discord Support Server: <a href="https://discord.gg/ProBot" className="text-blue-400 hover:text-blue-300">discord.gg/ProBot</a></li>
                  <li>Dashboard Support: Available in your ProBot dashboard</li>
                  <li>Email: <EMAIL></li>
                </ul>
                
                <div className="bg-blue-600/20 rounded-lg p-4 border border-blue-500/30 mt-4">
                  <h4 className="text-blue-300 font-semibold mb-2">💡 Tip</h4>
                  <p className="text-gray-300">
                    For faster support, include your Discord ID, server ID, and subscription details in your initial message.
                  </p>
                </div>
              </section>

            </div>
          </div>

          {/* Navigation Links */}
          <div className="mt-12 text-center">
            <div className="flex justify-center space-x-4">
              <Link 
                href="/terms-of-use"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                Terms of Use
              </Link>
              <Link 
                href="/privacy-policy"
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                Privacy Policy
              </Link>
              <Link 
                href="/"
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
              >
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
