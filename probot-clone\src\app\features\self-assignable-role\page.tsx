import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>, Fa<PERSON>ist<PERSON><PERSON>, Fa<PERSON>he<PERSON>, FaCog, FaReact } from 'react-icons/fa';

const features = [
  {
    icon: FaReact,
    title: 'Reaction Roles',
    description: 'Members get roles by reacting to messages with emojis',
  },
  {
    icon: FaMousePointer,
    title: 'Button Roles',
    description: 'Interactive buttons for easy role assignment',
  },
  {
    icon: FaListUl,
    title: 'Dropdown Menus',
    description: 'Organized dropdown menus for multiple role options',
  },
  {
    icon: FaCog,
    title: 'Advanced Settings',
    description: 'Customize role limits, requirements, and permissions',
  },
];

const roleTypes = [
  {
    name: 'Reaction Roles',
    description: 'Classic emoji-based role assignment',
    icon: '⚡',
    features: [
      'Custom emoji support',
      'Multiple reactions per message',
      'Role removal on unreact',
      'Custom embed messages',
    ],
  },
  {
    name: 'Button Roles',
    description: 'Modern button-based interface',
    icon: '🔘',
    features: [
      'Custom button labels',
      'Color-coded buttons',
      'Multiple rows support',
      'Persistent buttons',
    ],
  },
  {
    name: 'Dropdown Roles',
    description: 'Organized dropdown selection',
    icon: '📋',
    features: [
      'Categorized options',
      'Multi-select support',
      'Custom descriptions',
      'Placeholder text',
    ],
  },
];

export default function SelfAssignableRoles() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 bg-black/20 backdrop-blur-sm">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          <span className="text-white font-bold text-xl">ProBot</span>
        </div>
        
        <div className="hidden md:flex items-center space-x-8">
          <Link href="/" className="text-white hover:text-blue-300 transition-colors flex items-center">
            <FaHome className="mr-2" />
            Home
          </Link>
          <Link href="/dashboard" className="text-white hover:text-blue-300 transition-colors">
            Dashboard
          </Link>
          <Link href="/docs" className="text-white hover:text-blue-300 transition-colors">
            Docs
          </Link>
        </div>

        <Link 
          href="/dashboard"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
        >
          Dashboard
        </Link>
      </nav>

      {/* Header */}
      <div className="container mx-auto px-6 py-12">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-orange-500 rounded-lg flex items-center justify-center mr-4">
              <FaUsers className="text-white text-2xl" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Self-Assignable Roles
            </h1>
          </div>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Set up exclusive roles with various options that can be used to improve workflow and message style. Let your members choose their own roles with reactions, buttons, or dropdown menus.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {features.map((feature, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
              <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <feature.icon className="text-white text-xl" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
              <p className="text-gray-300 text-sm">{feature.description}</p>
            </div>
          ))}
        </div>

        {/* Role Types */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {roleTypes.map((type, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">{type.icon}</div>
                <h3 className="text-2xl font-bold text-white mb-2">{type.name}</h3>
                <p className="text-gray-300">{type.description}</p>
              </div>
              
              <div className="space-y-3">
                {type.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center">
                    <FaCheck className="text-green-400 mr-3 flex-shrink-0" />
                    <span className="text-gray-300 text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Setup Guide */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">How to Setup Self-Assignable Roles</h2>
          
          <div className="grid lg:grid-cols-2 gap-12">
            <div>
              <h3 className="text-xl font-semibold text-white mb-6">Step-by-Step Guide</h3>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white font-bold text-sm">1</span>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-2">Access Dashboard</h4>
                    <p className="text-gray-300">Go to the ProBot dashboard and select your server.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white font-bold text-sm">2</span>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-2">Navigate to Module</h4>
                    <p className="text-gray-300">Click on "Self-Assignable Roles" in the modules section.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white font-bold text-sm">3</span>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-2">Choose Type</h4>
                    <p className="text-gray-300">Select reaction roles, button roles, or dropdown menus.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white font-bold text-sm">4</span>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-2">Configure & Deploy</h4>
                    <p className="text-gray-300">Set up your roles, customize the message, and deploy to your channel.</p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-white mb-6">Important Notes</h3>
              
              <div className="space-y-4">
                <div className="bg-blue-600/20 rounded-lg p-4 border border-blue-500/30">
                  <h4 className="text-blue-300 font-semibold mb-2">💡 Tip</h4>
                  <p className="text-gray-300 text-sm">
                    Make sure ProBot's role is higher than the roles you want to assign in your server's role hierarchy.
                  </p>
                </div>

                <div className="bg-yellow-600/20 rounded-lg p-4 border border-yellow-500/30">
                  <h4 className="text-yellow-300 font-semibold mb-2">⚠️ Caution</h4>
                  <p className="text-gray-300 text-sm">
                    Test your role assignment in a private channel before deploying to public channels.
                  </p>
                </div>

                <div className="bg-green-600/20 rounded-lg p-4 border border-green-500/30">
                  <h4 className="text-green-300 font-semibold mb-2">✅ Best Practice</h4>
                  <p className="text-gray-300 text-sm">
                    Use clear role names and descriptions so members understand what each role provides.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Examples */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Role Assignment Examples</h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-black/20 rounded-lg p-6 border border-white/10">
              <h3 className="text-white font-semibold mb-4 flex items-center">
                <span className="mr-2">🎮</span>
                Gaming Roles
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <span className="mr-2">🔫</span>
                  <span className="text-gray-300">FPS Games</span>
                </div>
                <div className="flex items-center">
                  <span className="mr-2">⚔️</span>
                  <span className="text-gray-300">RPG Games</span>
                </div>
                <div className="flex items-center">
                  <span className="mr-2">🏎️</span>
                  <span className="text-gray-300">Racing Games</span>
                </div>
              </div>
            </div>

            <div className="bg-black/20 rounded-lg p-6 border border-white/10">
              <h3 className="text-white font-semibold mb-4 flex items-center">
                <span className="mr-2">🎨</span>
                Color Roles
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <span className="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                  <span className="text-gray-300">Red</span>
                </div>
                <div className="flex items-center">
                  <span className="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
                  <span className="text-gray-300">Blue</span>
                </div>
                <div className="flex items-center">
                  <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                  <span className="text-gray-300">Green</span>
                </div>
              </div>
            </div>

            <div className="bg-black/20 rounded-lg p-6 border border-white/10">
              <h3 className="text-white font-semibold mb-4 flex items-center">
                <span className="mr-2">📢</span>
                Notification Roles
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <span className="mr-2">📰</span>
                  <span className="text-gray-300">News Updates</span>
                </div>
                <div className="flex items-center">
                  <span className="mr-2">🎉</span>
                  <span className="text-gray-300">Events</span>
                </div>
                <div className="flex items-center">
                  <span className="mr-2">🎁</span>
                  <span className="text-gray-300">Giveaways</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features List */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Advanced Features</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-white mb-4">Role Management</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Role limits per user</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Required roles for access</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Blacklisted roles</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Auto role removal</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-white mb-4">Customization</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Custom embed messages</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Button colors and styles</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Custom emojis support</span>
                </div>
                <div className="flex items-center">
                  <FaCheck className="text-green-400 mr-3" />
                  <span className="text-gray-300">Role descriptions</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to set up self-assignable roles?
          </h2>
          <p className="text-gray-300 mb-8">
            Give your members the power to choose their own roles with our easy-to-use system
          </p>
          <div className="flex justify-center space-x-4">
            <Link 
              href="/dashboard"
              className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Setup Role Assignment
            </Link>
            <Link 
              href="/docs"
              className="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Read Documentation
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
