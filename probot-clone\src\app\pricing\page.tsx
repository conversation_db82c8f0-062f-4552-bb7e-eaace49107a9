import Link from 'next/link';
import { FaCheck, FaTimes, FaCrown, FaHome } from 'react-icons/fa';

const plans = [
  {
    name: 'Free',
    price: '$0',
    period: 'Forever',
    description: 'Perfect for small communities',
    features: [
      'Basic welcome messages',
      'Level system',
      'Basic moderation',
      'Reaction roles',
      'Basic logs',
      'Community support',
    ],
    limitations: [
      'Limited customization',
      'Basic features only',
      'No priority support',
    ],
    buttonText: 'Get Started',
    buttonClass: 'bg-gray-600 hover:bg-gray-700',
    popular: false,
  },
  {
    name: 'Tier 1',
    price: '$3',
    period: 'per month',
    description: 'Great for growing servers',
    features: [
      'Everything in Free',
      'Advanced welcome images',
      'Custom embed messages',
      'Advanced moderation',
      'Detailed logs',
      'Anti-raid protection',
      'Priority support',
      'Custom commands',
    ],
    limitations: [],
    buttonText: 'Choose Tier 1',
    buttonClass: 'bg-blue-600 hover:bg-blue-700',
    popular: true,
  },
  {
    name: 'Tier 2',
    price: '$5',
    period: 'per month',
    description: 'Perfect for large communities',
    features: [
      'Everything in Tier 1',
      'VIP protection',
      'Advanced anti-raid',
      'Custom bot status',
      'Premium support',
      'Early access to features',
      'Multiple server support',
      'Advanced analytics',
    ],
    limitations: [],
    buttonText: 'Choose Tier 2',
    buttonClass: 'bg-purple-600 hover:bg-purple-700',
    popular: false,
  },
];

const faqs = [
  {
    question: 'What payment methods do you accept?',
    answer: 'We accept all major credit cards, PayPal, and various other payment methods through our secure payment processor.',
  },
  {
    question: 'Can I cancel my subscription anytime?',
    answer: 'Yes, you can cancel your subscription at any time. Your premium features will remain active until the end of your current billing period.',
  },
  {
    question: 'Do you offer refunds?',
    answer: 'We offer a 7-day money-back guarantee for all premium subscriptions. Please check our refund policy for more details.',
  },
  {
    question: 'Can I upgrade or downgrade my plan?',
    answer: 'Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.',
  },
];

export default function Pricing() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 bg-black/20 backdrop-blur-sm">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          <span className="text-white font-bold text-xl">ProBot</span>
        </div>
        
        <div className="hidden md:flex items-center space-x-8">
          <Link href="/" className="text-white hover:text-blue-300 transition-colors flex items-center">
            <FaHome className="mr-2" />
            Home
          </Link>
          <Link href="/dashboard" className="text-white hover:text-blue-300 transition-colors">
            Dashboard
          </Link>
          <Link href="/commands" className="text-white hover:text-blue-300 transition-colors">
            Commands
          </Link>
        </div>

        <Link 
          href="/dashboard"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
        >
          Dashboard
        </Link>
      </nav>

      {/* Header */}
      <div className="container mx-auto px-6 py-12">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
            Choose Your Plan
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Unlock premium features and take your Discord server to the next level
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <div 
              key={index} 
              className={`relative bg-white/10 backdrop-blur-sm rounded-2xl p-8 border ${
                plan.popular ? 'border-blue-500 ring-2 ring-blue-500/50' : 'border-white/20'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center">
                    <FaCrown className="mr-1" />
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-white">{plan.price}</span>
                  <span className="text-gray-300 ml-2">/ {plan.period}</span>
                </div>
                <p className="text-gray-300">{plan.description}</p>
              </div>

              <div className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center">
                    <FaCheck className="text-green-400 mr-3 flex-shrink-0" />
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
                
                {plan.limitations.map((limitation, limitIndex) => (
                  <div key={limitIndex} className="flex items-center">
                    <FaTimes className="text-red-400 mr-3 flex-shrink-0" />
                    <span className="text-gray-400">{limitation}</span>
                  </div>
                ))}
              </div>

              <button className={`w-full ${plan.buttonClass} text-white py-3 rounded-lg font-semibold transition-colors`}>
                {plan.buttonText}
              </button>
            </div>
          ))}
        </div>

        {/* Features Comparison */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Feature Comparison</h2>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left text-white font-semibold py-4">Features</th>
                  <th className="text-center text-white font-semibold py-4">Free</th>
                  <th className="text-center text-white font-semibold py-4">Tier 1</th>
                  <th className="text-center text-white font-semibold py-4">Tier 2</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                <tr className="border-b border-white/10">
                  <td className="py-4">Welcome Messages</td>
                  <td className="text-center py-4"><FaCheck className="text-green-400 mx-auto" /></td>
                  <td className="text-center py-4"><FaCheck className="text-green-400 mx-auto" /></td>
                  <td className="text-center py-4"><FaCheck className="text-green-400 mx-auto" /></td>
                </tr>
                <tr className="border-b border-white/10">
                  <td className="py-4">Level System</td>
                  <td className="text-center py-4"><FaCheck className="text-green-400 mx-auto" /></td>
                  <td className="text-center py-4"><FaCheck className="text-green-400 mx-auto" /></td>
                  <td className="text-center py-4"><FaCheck className="text-green-400 mx-auto" /></td>
                </tr>
                <tr className="border-b border-white/10">
                  <td className="py-4">Anti-Raid Protection</td>
                  <td className="text-center py-4"><FaTimes className="text-red-400 mx-auto" /></td>
                  <td className="text-center py-4"><FaCheck className="text-green-400 mx-auto" /></td>
                  <td className="text-center py-4"><FaCheck className="text-green-400 mx-auto" /></td>
                </tr>
                <tr className="border-b border-white/10">
                  <td className="py-4">VIP Protection</td>
                  <td className="text-center py-4"><FaTimes className="text-red-400 mx-auto" /></td>
                  <td className="text-center py-4"><FaTimes className="text-red-400 mx-auto" /></td>
                  <td className="text-center py-4"><FaCheck className="text-green-400 mx-auto" /></td>
                </tr>
                <tr className="border-b border-white/10">
                  <td className="py-4">Priority Support</td>
                  <td className="text-center py-4"><FaTimes className="text-red-400 mx-auto" /></td>
                  <td className="text-center py-4"><FaCheck className="text-green-400 mx-auto" /></td>
                  <td className="text-center py-4"><FaCheck className="text-green-400 mx-auto" /></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-black/20 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-3">{faq.question}</h3>
                <p className="text-gray-300">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to upgrade your Discord server?
          </h2>
          <p className="text-gray-300 mb-8">
            Join thousands of servers already using ProBot Premium
          </p>
          <div className="flex justify-center space-x-4">
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
              Start Free Trial
            </button>
            <Link 
              href="/dashboard"
              className="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Go to Dashboard
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
