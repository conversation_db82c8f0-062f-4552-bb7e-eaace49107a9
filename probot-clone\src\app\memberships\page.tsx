import Link from 'next/link';
import { FaHome, FaCrown, FaCheck, FaStar, FaShieldAlt, FaRocket, FaGem } from 'react-icons/fa';

const membershipTiers = [
  {
    name: 'Bronze',
    price: '$2',
    period: 'per month',
    color: 'from-amber-600 to-amber-800',
    icon: FaStar,
    description: 'Perfect for small communities',
    features: [
      'Remove ProBot branding',
      'Custom bot status',
      'Priority support',
      'Early access to features',
      'Custom welcome images',
      'Advanced embed customization',
    ],
    servers: '1 server',
    buttonClass: 'bg-amber-600 hover:bg-amber-700',
  },
  {
    name: 'Silver',
    price: '$5',
    period: 'per month',
    color: 'from-gray-400 to-gray-600',
    icon: FaShieldAlt,
    description: 'Great for growing servers',
    features: [
      'Everything in Bronze',
      'Anti-raid protection',
      'VIP protection',
      'Advanced moderation',
      'Custom commands',
      'Detailed analytics',
      'Multiple language support',
    ],
    servers: '3 servers',
    buttonClass: 'bg-gray-600 hover:bg-gray-700',
    popular: true,
  },
  {
    name: 'Gold',
    price: '$10',
    period: 'per month',
    color: 'from-yellow-400 to-yellow-600',
    icon: FaCrown,
    description: 'Perfect for large communities',
    features: [
      'Everything in Silver',
      'Unlimited servers',
      'Custom bot avatar',
      'Dedicated support',
      'API access',
      'White-label solution',
      'Custom integrations',
    ],
    servers: 'Unlimited servers',
    buttonClass: 'bg-yellow-600 hover:bg-yellow-700',
  },
  {
    name: 'Diamond',
    price: '$20',
    period: 'per month',
    color: 'from-blue-400 to-purple-600',
    icon: FaGem,
    description: 'Enterprise solution',
    features: [
      'Everything in Gold',
      'Private bot instance',
      'Custom features development',
      '24/7 dedicated support',
      'SLA guarantee',
      'Custom branding',
      'Enterprise integrations',
    ],
    servers: 'Private instance',
    buttonClass: 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700',
  },
];

const benefits = [
  {
    icon: FaRocket,
    title: 'Enhanced Performance',
    description: 'Faster response times and priority processing for your commands',
  },
  {
    icon: FaShieldAlt,
    title: 'Advanced Protection',
    description: 'Anti-raid, VIP protection, and advanced security features',
  },
  {
    icon: FaCrown,
    title: 'Exclusive Features',
    description: 'Access to premium-only features and early beta releases',
  },
  {
    icon: FaStar,
    title: 'Priority Support',
    description: 'Get help faster with our priority support queue',
  },
];

export default function Memberships() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900">
      {/* Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 bg-black/20 backdrop-blur-sm">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          <span className="text-white font-bold text-xl">ProBot</span>
        </div>
        
        <div className="hidden md:flex items-center space-x-8">
          <Link href="/" className="text-white hover:text-blue-300 transition-colors flex items-center">
            <FaHome className="mr-2" />
            Home
          </Link>
          <Link href="/dashboard" className="text-white hover:text-blue-300 transition-colors">
            Dashboard
          </Link>
          <Link href="/pricing" className="text-white hover:text-blue-300 transition-colors">
            Pricing
          </Link>
        </div>

        <Link 
          href="/dashboard"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
        >
          Dashboard
        </Link>
      </nav>

      {/* Header */}
      <div className="container mx-auto px-6 py-12">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold mb-4">
            <FaStar className="mr-2" />
            NEW: Membership Subscriptions
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
            ProBot Memberships
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Unlock the full potential of ProBot with our premium membership tiers. Get exclusive features, priority support, and advanced customization options.
          </p>
        </div>

        {/* Benefits Section */}
        <div className="grid md:grid-cols-4 gap-6 mb-16">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 text-center">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                <benefit.icon className="text-white text-xl" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">{benefit.title}</h3>
              <p className="text-gray-300 text-sm">{benefit.description}</p>
            </div>
          ))}
        </div>

        {/* Membership Tiers */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {membershipTiers.map((tier, index) => (
            <div 
              key={index} 
              className={`relative bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 ${
                tier.popular ? 'ring-2 ring-blue-500' : ''
              }`}
            >
              {tier.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-6">
                <div className={`w-16 h-16 bg-gradient-to-r ${tier.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                  <tier.icon className="text-white text-2xl" />
                </div>
                
                <h3 className="text-xl font-bold text-white mb-2">{tier.name}</h3>
                <div className="mb-2">
                  <span className="text-3xl font-bold text-white">{tier.price}</span>
                  <span className="text-gray-300 text-sm">/{tier.period}</span>
                </div>
                <p className="text-gray-300 text-sm mb-2">{tier.description}</p>
                <p className="text-blue-300 text-sm font-semibold">{tier.servers}</p>
              </div>

              <div className="space-y-3 mb-6">
                {tier.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center text-sm">
                    <FaCheck className="text-green-400 mr-2 flex-shrink-0" />
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>

              <button className={`w-full ${tier.buttonClass} text-white py-3 rounded-lg font-semibold transition-colors`}>
                Choose {tier.name}
              </button>
            </div>
          ))}
        </div>

        {/* Comparison Table */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Feature Comparison</h2>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left text-white font-semibold py-4">Features</th>
                  <th className="text-center text-white font-semibold py-4">Bronze</th>
                  <th className="text-center text-white font-semibold py-4">Silver</th>
                  <th className="text-center text-white font-semibold py-4">Gold</th>
                  <th className="text-center text-white font-semibold py-4">Diamond</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                <tr className="border-b border-white/10">
                  <td className="py-3">Custom Bot Status</td>
                  <td className="text-center py-3"><FaCheck className="text-green-400 mx-auto" /></td>
                  <td className="text-center py-3"><FaCheck className="text-green-400 mx-auto" /></td>
                  <td className="text-center py-3"><FaCheck className="text-green-400 mx-auto" /></td>
                  <td className="text-center py-3"><FaCheck className="text-green-400 mx-auto" /></td>
                </tr>
                <tr className="border-b border-white/10">
                  <td className="py-3">Anti-Raid Protection</td>
                  <td className="text-center py-3">-</td>
                  <td className="text-center py-3"><FaCheck className="text-green-400 mx-auto" /></td>
                  <td className="text-center py-3"><FaCheck className="text-green-400 mx-auto" /></td>
                  <td className="text-center py-3"><FaCheck className="text-green-400 mx-auto" /></td>
                </tr>
                <tr className="border-b border-white/10">
                  <td className="py-3">API Access</td>
                  <td className="text-center py-3">-</td>
                  <td className="text-center py-3">-</td>
                  <td className="text-center py-3"><FaCheck className="text-green-400 mx-auto" /></td>
                  <td className="text-center py-3"><FaCheck className="text-green-400 mx-auto" /></td>
                </tr>
                <tr className="border-b border-white/10">
                  <td className="py-3">Private Bot Instance</td>
                  <td className="text-center py-3">-</td>
                  <td className="text-center py-3">-</td>
                  <td className="text-center py-3">-</td>
                  <td className="text-center py-3"><FaCheck className="text-green-400 mx-auto" /></td>
                </tr>
                <tr className="border-b border-white/10">
                  <td className="py-3">Server Limit</td>
                  <td className="text-center py-3">1</td>
                  <td className="text-center py-3">3</td>
                  <td className="text-center py-3">Unlimited</td>
                  <td className="text-center py-3">Private</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Membership FAQ</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-black/20 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-3">Can I change my membership tier?</h3>
              <p className="text-gray-300">Yes, you can upgrade or downgrade your membership at any time. Changes take effect immediately.</p>
            </div>
            
            <div className="bg-black/20 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-3">What happens if I cancel?</h3>
              <p className="text-gray-300">Your membership benefits will remain active until the end of your current billing period.</p>
            </div>
            
            <div className="bg-black/20 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-3">Do you offer refunds?</h3>
              <p className="text-gray-300">We offer a 7-day money-back guarantee for all new memberships.</p>
            </div>
            
            <div className="bg-black/20 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-3">Is there a free trial?</h3>
              <p className="text-gray-300">Yes, we offer a 3-day free trial for all membership tiers to new users.</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to unlock ProBot's full potential?
          </h2>
          <p className="text-gray-300 mb-8">
            Join thousands of servers already using ProBot Premium features
          </p>
          <div className="flex justify-center space-x-4">
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
              Start Free Trial
            </button>
            <Link 
              href="/pricing"
              className="bg-gray-600 hover:bg-gray-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Compare Plans
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
