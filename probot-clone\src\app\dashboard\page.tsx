'use client';

import { useState } from 'react';
import Link from 'next/link';
import { FaDiscord, FaCog, FaUsers, FaShieldAlt, FaRobot, FaChartLine, FaPalette, FaVolumeUp, FaStar, FaLink, FaEye, FaHome, FaSignOutAlt } from 'react-icons/fa';

const modules = [
  { id: 'welcome', name: 'Welcome & Goodbye', icon: FaUsers, description: 'Customize welcome and goodbye messages', color: 'bg-[#57f287]' },
  { id: 'autoresponder', name: 'Auto Responder', icon: FaRobot, description: 'Set up automatic responses', color: 'bg-[#5865f2]' },
  { id: 'embed', name: 'Embed Messages', icon: FaCog, description: 'Create rich embed messages', color: 'bg-[#5865f2]' },
  { id: 'leveling', name: 'Level System', icon: FaChartLine, description: 'Reward active members with levels', color: 'bg-[#fee75c]' },
  { id: 'autoroles', name: 'Auto-Roles', icon: FaShieldAlt, description: 'Automatically assign roles to new members', color: 'bg-[#ed4245]' },
  { id: 'logs', name: 'Logs', icon: FaEye, description: 'Monitor all server activities', color: 'bg-[#5865f2]' },
  { id: 'colors', name: 'Colors', icon: FaPalette, description: 'Let members choose color roles', color: 'bg-[#eb459e]' },
  { id: 'reaction-roles', name: 'Self-Assignable Roles', icon: FaUsers, description: 'Reaction and button roles', color: 'bg-[#faa61a]' },
  { id: 'temp-channels', name: 'Temporary Channels', icon: FaVolumeUp, description: 'Create temporary voice channels', color: 'bg-[#00d9ff]' },
  { id: 'temp-links', name: 'Temp Links', icon: FaLink, description: 'Generate temporary invite links', color: 'bg-[#00d9ff]' },
  { id: 'voice-online', name: 'Voice Online', icon: FaVolumeUp, description: 'Show voice channel activity', color: 'bg-[#57f287]' },
  { id: 'anti-raid', name: 'Anti-Raid', icon: FaShieldAlt, description: 'Protect against raids', color: 'bg-[#ed4245]', premium: true },
  { id: 'vip-protection', name: 'VIP Protection', icon: FaShieldAlt, description: 'Advanced server protection', color: 'bg-[#faa61a]', premium: true },
  { id: 'starboard', name: 'Starboard', icon: FaStar, description: 'Highlight popular messages', color: 'bg-[#fee75c]' },
  { id: 'automod', name: 'Automod', icon: FaRobot, description: 'Automatic moderation system', color: 'bg-[#72767d]' },
];

export default function Dashboard() {
  const [selectedServer, setSelectedServer] = useState('My Discord Server');
  const [selectedModule, setSelectedModule] = useState('welcome');

  return (
    <div className="min-h-screen bg-[#36393f] flex">
      <div className="w-80 bg-[#2f3136] border-r border-[#40444b] flex flex-col">
        <div className="p-6 border-b border-[#40444b]">
          <Link href="/" className="flex items-center space-x-2 mb-4">
            <div className="w-8 h-8 bg-[#5865f2] rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">P</span>
            </div>
            <span className="text-white font-bold text-xl">ProBot</span>
          </Link>

          <div className="relative">
            <select
              value={selectedServer}
              onChange={(e) => setSelectedServer(e.target.value)}
              className="w-full bg-[#40444b] text-white p-3 rounded border border-[#72767d] focus:border-[#5865f2] focus:outline-none"
            >
              <option value="My Discord Server">My Discord Server</option>
              <option value="Gaming Community">Gaming Community</option>
              <option value="Study Group">Study Group</option>
            </select>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            <Link
              href="/"
              className="flex items-center space-x-3 p-3 rounded text-[#b9bbbe] hover:bg-[#40444b] hover:text-white transition-colors mb-2"
            >
              <FaHome />
              <span>Home</span>
            </Link>

            <div className="mt-6">
              <h3 className="text-[#72767d] text-sm font-semibold uppercase tracking-wider mb-3">Modules</h3>
              <div className="space-y-1">
                {modules.map((module) => (
                  <button
                    key={module.id}
                    onClick={() => setSelectedModule(module.id)}
                    className={`w-full flex items-center space-x-3 p-3 rounded transition-colors text-left ${
                      selectedModule === module.id
                        ? 'bg-[#5865f2] text-white'
                        : 'text-[#b9bbbe] hover:bg-[#40444b] hover:text-white'
                    }`}
                  >
                    <div className={`w-8 h-8 ${module.color} rounded flex items-center justify-center`}>
                      <module.icon className="text-white text-sm" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center">
                        <span className="text-sm font-medium">{module.name}</span>
                        {module.premium && (
                          <span className="ml-2 text-xs bg-[#faa61a] text-black px-1 rounded">PRO</span>
                        )}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="p-4 border-t border-[#40444b]">
          <button className="flex items-center space-x-3 p-3 rounded text-[#b9bbbe] hover:bg-[#40444b] hover:text-white transition-colors w-full text-left">
            <FaSignOutAlt />
            <span>Logout</span>
          </button>
        </div>
      </div>

      <div className="flex-1 flex flex-col">
        <div className="bg-[#2f3136] border-b border-[#40444b] p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white">
                {modules.find(m => m.id === selectedModule)?.name || 'Dashboard'}
              </h1>
              <p className="text-[#b9bbbe] mt-1">
                {modules.find(m => m.id === selectedModule)?.description || 'Manage your server settings'}
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <Link
                href="/commands"
                className="flex items-center space-x-2 text-[#b9bbbe] hover:text-white transition-colors"
              >
                <FaCog />
                <span>Commands</span>
              </Link>

              <Link
                href="https://discord.gg/ProBot"
                className="flex items-center space-x-2 text-[#b9bbbe] hover:text-white transition-colors"
              >
                <FaDiscord />
                <span>Support</span>
              </Link>
            </div>
          </div>
        </div>

        <div className="flex-1 p-6 bg-[#36393f]">
          <div className="max-w-4xl mx-auto">
            <div className="bg-[#2f3136] rounded p-6 border border-[#40444b]">
              <h2 className="text-xl font-semibold text-white mb-4">
                {modules.find(m => m.id === selectedModule)?.name} Configuration
              </h2>
              <p className="text-[#b9bbbe] mb-6">
                Configure your {modules.find(m => m.id === selectedModule)?.name.toLowerCase()} settings here.
              </p>

              <div className="bg-[#40444b] rounded p-8 text-center">
                <div className={`w-16 h-16 ${modules.find(m => m.id === selectedModule)?.color} rounded flex items-center justify-center mx-auto mb-4`}>
                  <FaCog className="text-white text-2xl" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  {modules.find(m => m.id === selectedModule)?.name} Settings
                </h3>
                <p className="text-[#b9bbbe] mb-4">
                  This module configuration panel is coming soon. Stay tuned for updates!
                </p>
                <button className="bg-[#5865f2] hover:bg-[#4752c4] text-white px-6 py-2 rounded transition-colors">
                  Enable Module
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
